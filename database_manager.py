"""
数据库连接管理器
提供高性能的 SQLite 数据库连接管理和优化功能
"""

import sqlite3
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, Tuple, Union


class DatabaseManager:
    """
    高性能 SQLite 数据库连接管理器
    
    特性：
    - 连接复用，减少连接创建/销毁开销
    - 自动性能优化设置（WAL模式、缓存等）
    - 事务批处理支持
    - 线程安全
    - 向后兼容的 API
    """
    
    def __init__(self, db_path: str, enable_optimizations: bool = True):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
            enable_optimizations: 是否启用性能优化设置
        """
        self.db_path = db_path
        self.enable_optimizations = enable_optimizations
        self._conn = None
        self._lock = threading.RLock()  # 支持重入锁
        self._transaction_depth = 0
        
    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接，如果不存在则创建"""
        if self._conn is None:
            self._conn = sqlite3.connect(self.db_path, check_same_thread=False)
            if self.enable_optimizations:
                self._apply_performance_optimizations()
        return self._conn
    
    def _apply_performance_optimizations(self):
        """应用性能优化设置"""
        if self._conn is None:
            return
            
        try:
            # WAL 模式：提升并发性能，持久化设置
            self._conn.execute('PRAGMA journal_mode=WAL')
            
            # 同步模式：NORMAL 在 WAL 模式下是安全的，比 FULL 更快
            self._conn.execute('PRAGMA synchronous=NORMAL')
            
            # 增加页面缓存大小（10MB，约10000页）
            self._conn.execute('PRAGMA cache_size=10000')
            
            # 启用内存映射 I/O（256MB）
            self._conn.execute('PRAGMA mmap_size=268435456')
            
            # 设置临时存储为内存
            self._conn.execute('PRAGMA temp_store=MEMORY')
            
            # 优化查询计划器
            self._conn.execute('PRAGMA optimize')
            
            print("数据库性能优化设置已应用")
            
        except Exception as e:
            print(f"应用性能优化设置时出错: {e}")
    
    @contextmanager
    def get_connection(self):
        """
        获取数据库连接的上下文管理器
        
        使用示例:
            with db_manager.get_connection() as conn:
                cursor = conn.execute("SELECT * FROM table")
                results = cursor.fetchall()
        """
        with self._lock:
            conn = self._get_connection()
            try:
                yield conn
            except Exception as e:
                if self._transaction_depth > 0:
                    conn.rollback()
                    self._transaction_depth = 0
                raise e
    
    @contextmanager
    def transaction(self):
        """
        事务上下文管理器
        
        使用示例:
            with db_manager.transaction() as conn:
                conn.execute("INSERT INTO table VALUES (?)", (value,))
                conn.execute("UPDATE table SET col=? WHERE id=?", (new_val, id))
        """
        with self._lock:
            conn = self._get_connection()
            if self._transaction_depth == 0:
                conn.execute('BEGIN')
            self._transaction_depth += 1
            
            try:
                yield conn
                self._transaction_depth -= 1
                if self._transaction_depth == 0:
                    conn.commit()
            except Exception as e:
                self._transaction_depth = 0
                conn.rollback()
                raise e
    
    def safe_execute(self, query: str, params: Optional[Tuple] = None, 
                    fetch: Union[bool, str] = False, commit: bool = True) -> Any:
        """
        安全执行数据库操作（向后兼容方法）
        
        Args:
            query: SQL 查询语句
            params: 查询参数
            fetch: 是否获取结果 ('one', 'all', True, False)
            commit: 是否提交事务
            
        Returns:
            查询结果或 cursor 对象
        """
        try:
            with self._lock:
                conn = self._get_connection()
                cursor = conn.execute(query, params or ())
                
                if commit and self._transaction_depth == 0:
                    conn.commit()
                
                if fetch:
                    if fetch == 'one':
                        return cursor.fetchone()
                    elif fetch == 'all' or fetch is True:
                        return cursor.fetchall()
                    else:
                        return cursor.fetchall()
                
                return cursor
                
        except Exception as e:
            print(f"数据库操作失败: {e}")
            return None
    
    def execute_batch(self, operations: List[Dict[str, Any]]) -> bool:
        """
        批量执行数据库操作
        
        Args:
            operations: 操作列表，每个操作包含 'query' 和可选的 'params'
            
        Returns:
            是否成功执行所有操作
            
        使用示例:
            operations = [
                {'query': 'INSERT INTO table VALUES (?, ?)', 'params': (1, 'a')},
                {'query': 'INSERT INTO table VALUES (?, ?)', 'params': (2, 'b')},
                {'query': 'UPDATE table SET col=? WHERE id=?', 'params': ('new', 1)}
            ]
            success = db_manager.execute_batch(operations)
        """
        try:
            with self.transaction() as conn:
                for op in operations:
                    query = op['query']
                    params = op.get('params', ())
                    conn.execute(query, params)
            return True
            
        except Exception as e:
            print(f"批量操作失败: {e}")
            return False
    
    def execute_many(self, query: str, params_list: List[Tuple]) -> bool:
        """
        执行多个相同查询的批量操作
        
        Args:
            query: SQL 查询语句
            params_list: 参数列表
            
        Returns:
            是否成功执行
        """
        try:
            with self.transaction() as conn:
                conn.executemany(query, params_list)
            return True
            
        except Exception as e:
            print(f"批量执行失败: {e}")
            return False
    
    def optimize_database(self):
        """手动优化数据库"""
        try:
            with self.get_connection() as conn:
                conn.execute('PRAGMA optimize')
                print("数据库优化完成")
        except Exception as e:
            print(f"数据库优化失败: {e}")
    
    def get_database_stats(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        stats = {}
        try:
            with self.get_connection() as conn:
                # 获取数据库大小
                cursor = conn.execute("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
                size = cursor.fetchone()[0]
                stats['database_size_bytes'] = size
                stats['database_size_mb'] = round(size / (1024 * 1024), 2)
                
                # 获取表数量
                cursor = conn.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
                stats['table_count'] = cursor.fetchone()[0]
                
                # 获取索引数量
                cursor = conn.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index'")
                stats['index_count'] = cursor.fetchone()[0]
                
                # 获取 WAL 模式状态
                cursor = conn.execute("PRAGMA journal_mode")
                stats['journal_mode'] = cursor.fetchone()[0]
                
        except Exception as e:
            print(f"获取数据库统计信息失败: {e}")
            
        return stats
    
    def close(self):
        """关闭数据库连接"""
        with self._lock:
            if self._conn:
                try:
                    # 在关闭前运行最后一次优化
                    self._conn.execute('PRAGMA optimize')
                    self._conn.close()
                except Exception as e:
                    print(f"关闭数据库连接时出错: {e}")
                finally:
                    self._conn = None
                    self._transaction_depth = 0
    
    def __enter__(self):
        """支持 with 语句"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """支持 with 语句"""
        self.close()
    
    def __del__(self):
        """析构函数，确保连接被关闭"""
        self.close()
